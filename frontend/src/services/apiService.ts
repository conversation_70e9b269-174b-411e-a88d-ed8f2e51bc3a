import axios from 'axios';
import type {
  ChatRequest,
  ConfigResponse,
  ConversationDetail,
  ConversationSummary,
  EndEvent,
  SearchPreviewEvent,
  SourceEvent} from '../types/apiTypes';

// Base API URL
const API_BASE_URL = '/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API endpoints
export const fetchConfig = async (): Promise<ConfigResponse> => {
  const response = await api.get<ConfigResponse>('/config/models/');
  return response.data;
};

export const fetchChatHistory = async (userId: string): Promise<ConversationSummary[]> => {
  const response = await api.get<ConversationSummary[]>(`/chat/history/?userId=${userId}`);
  return response.data;
};

export const fetchConversationMessages = async (
  userId: string,
  conversationId: string
): Promise<ConversationDetail> => {
  const response = await api.get<ConversationDetail>(
    `/chat/history/${conversationId}/?userId=${userId}`
  );
  return response.data;
};

export const submitFeedback = async (
  messageId: string,
  feedbackType: 'like' | 'dislike' | null
): Promise<any> => {
  const response = await api.post<any>(
    '/feedback/',
    { messageId, feedbackType }
  );
  return response.data;
};

export const deleteConversation = async (
  userId: string,
  conversationId: string
): Promise<{ status: string }> => {
  const response = await api.delete<{ status: string }>(
    `/chat/history/${conversationId}/delete/?userId=${userId}`
  );
  return response.data;
};

// SSE streaming interface
interface SSECallbacks {
  onOpen?: () => void;
  onStatus?: (status: string) => void;
  onToken?: (token: string, isFirst: boolean) => void;
  onSearchPreview?: (preview: SearchPreviewEvent) => void;
  onSource?: (source: SourceEvent) => void;
  onEnd?: (data: EndEvent) => void;
  onError?: (error: Error) => void;
}

export const postChatMessageSSE = async (
  chatRequest: ChatRequest,
  callbacks: SSECallbacks
): Promise<void> => {
  try {
    const { onOpen, onStatus, onToken, onSearchPreview, onSource, onEnd, onError } = callbacks;

    // Directly use the fetch-based implementation:
    const url = `${API_BASE_URL}/chat/`;
    const body = JSON.stringify({
      message: chatRequest.message,
      userId: chatRequest.userId,
      conversationId: chatRequest.conversationId || null,
      model: chatRequest.model || undefined // Ensure model is included
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream', // Primarily request event-stream
        'Cache-Control': 'no-cache',
      },
      body: body,
    });

    if (!response.ok) {
      let errorMsg = `Server responded with ${response.status}: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.detail) {
          errorMsg = errorData.detail;
        } else if (typeof errorData === 'string' && errorData.length > 0) {
          errorMsg = errorData;
        } else if (errorData) {
          errorMsg = JSON.stringify(errorData);
        }
      } catch (e) {
        // If parsing error response as JSON fails, try to read as text
        try {
            const textError = await response.text();
            if (textError && textError.length > 0 && textError.length < 200) { // Avoid huge HTML pages
                 errorMsg = textError;
            }
        } catch (textE) {
            // Ignore error from .text() if it also fails
        }
      }
      throw new Error(errorMsg);
    }

    // Check if the response is actually an event-stream
    if (!response.headers.get('content-type')?.includes('text/event-stream')) {
      console.warn('Expected text/event-stream, but received:', response.headers.get('content-type'));
      try {
        const fallbackJson = await response.json();
        if (onToken && typeof fallbackJson.response === 'string') {
            onToken(fallbackJson.response, true);
        } else {
            // If there's no specific 'response' field, send the whole JSON stringified
            if (onToken) onToken(JSON.stringify(fallbackJson), true);
        }
        if (onEnd) onEnd({
            conversationId: fallbackJson.conversationId || chatRequest.conversationId || 'unknown',
            userMessageId: fallbackJson.userMessageId || 'unknown',
            aiMessageId: fallbackJson.aiMessageId || 'unknown'
        });
        return; // Stop further SSE processing
      } catch (e) {
        // If parsing as JSON fails, read as text
        const responseText = await response.text();
        throw new Error(`Expected text/event-stream, got ${response.headers.get('content-type')}. Response: ${responseText.substring(0,100)}`);
      }
    }

    if (onOpen) onOpen();

    // Create a reader from the response body
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is not readable');
    }

    // Process the stream
    const decoder = new TextDecoder();
    let buffer = '';

    // Manual event parsing function
    const processEvents = (chunk: string) => {
      buffer += chunk;

      // Split by double newlines (event separator)
      const events = buffer.split('\n\n');

      // Keep the last incomplete event in the buffer
      buffer = events.pop() || '';

      for (const event of events) {
        if (!event.trim()) continue;

        const lines = event.split('\n');
        let eventType = '';
        let data = '';

        for (const line of lines) {
          if (line.startsWith('event:')) {
            eventType = line.substring(6).trim();
          } else if (line.startsWith('data:')) {
            data = line.substring(5).trim();
          }
        }

        if (eventType && data) {
          try {
            const parsedData = JSON.parse(data);

            switch (eventType) {
              case 'status':
                if (onStatus) onStatus(parsedData.content);
                break;
              case 'token':
                if (onToken) onToken(parsedData.content, !!parsedData.isFirst);
                break;
              case 'search_preview':
                if (onSearchPreview) onSearchPreview(parsedData);
                break;
              case 'source':
                if (onSource) onSource(parsedData);
                break;
              case 'end':
                if (onEnd) onEnd(parsedData);
                return true; // Signal to stop reading
            }
          } catch (e) {
            console.error('Error parsing event data:', e);
            // If individual event parsing fails, call onError for that event but don't stop stream.
            if (onError) onError(new Error(`Error parsing event data: ${data}. Details: ${e}`));
          }
        }
      }

      return false; // Continue reading
    };

    // Read the stream
    const readStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // If stream ends and buffer has content, process it
            if (buffer.trim()) {
                processEvents(''); // Process any remaining buffer content
            }
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const shouldStop = processEvents(chunk);

          if (shouldStop) {
            break;
          }
        }
      } catch (error) {
        console.error('Stream reading error:', error);
        if (onError) onError(error instanceof Error ? error : new Error('Stream reading error'));
      } finally {
        reader.releaseLock();
      }
    };

    // Start reading
    readStream();
  } catch (error) {
    console.error('API error in postChatMessageSSE:', error); // Clarified error source
    if (callbacks.onError) {
      callbacks.onError(error instanceof Error ? error : new Error('Unknown API error')); // Clarified error message
    }
  }
};
