import React, { useEffect, useRef, useState, useLayoutEffect } from 'react';
import { useChatStore } from '../../stores/chatStore';
import ChatInput from '../Input/ChatInput';
import AsyncMarkdown from '../Common/AsyncMarkdown';

const ChatWindow: React.FC = () => {
  const {
    messages,
    isSendingMessage,
    currentStatus,
    aiTyping,
    sendMessage,
    loadConversation,
    submitFeedback
  } = useChatStore();

  const [showGreeting, setShowGreeting] = useState(true);
  const chatListRef = useRef<HTMLUListElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      });
    }
  };
  
  useLayoutEffect(() => {
    const listElement = chatListRef.current;
    if (!listElement) return;
    const observer = new MutationObserver((_mutationsList, _observer) => {
      scrollToBottom();
    });

    observer.observe(listElement, { childList: true, subtree: true });
    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    const conversationId = localStorage.getItem('currentConversationId');
    if (conversationId) {
      loadConversation(conversationId);
    }
  }, [loadConversation]);

  // Hide greeting when messages exist
  useEffect(() => {
    if (messages.length > 0) {
      setShowGreeting(false);
    } else {
      setShowGreeting(true);
    }
  }, [messages]);

  const handleSendMessage = (text: string) => {
    sendMessage(text);
  };

  const handleFeedback = (messageId: string, type: 'like' | 'dislike') => {
    submitFeedback(messageId, type);
  };

  const renderMessages = () => {
    return messages.map((message) => {
      const isUser = message.role === 'user';
      const isAssistant = message.role === 'assistant';

      return (
        <li key={message.id} className={isUser ? 'chat-prompt' : 'chat-reply'}>
          <div className="msg-list">
            {isAssistant && message.isLoading ? (
              <div className="loading-container">
                <i className="fa-solid fa-spinner"></i>
                <span>AI is thinking...</span>
              </div>
            ) : null}

            {isUser ? (
              <span>{message.content}</span>
            ) : (
              <AsyncMarkdown content={message.content} />
            )}

            {isAssistant && (
              <div className="msg-option">
                <span><i className="ri-clipboard-line" onClick={() => {
                  navigator.clipboard.writeText(message.content)
                  .then(() => {
                    console.log('Text copied to clipboard');
                  })
                  .catch(err => {
                    console.error('Failed to copy text: ', err);
                  });
                }}></i></span>
                <span>
                  <i
                    className={`ri-thumb-up-line ${message.feedback === 'like' ? 'active' : ''}`}
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default scroll-to-top behavior
                      handleFeedback(message.id, 'like');
                    }}
                  ></i>
                </span>
                <span>
                  <i
                    className={`ri-thumb-down-line ${message.feedback === 'dislike' ? 'active' : ''}`}
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default scroll-to-top behavior
                      handleFeedback(message.id, 'dislike');
                    }}
                  ></i>
                </span>
              </div>
            )}
          </div>
        </li>
      );
    });
  };

  return (
    <div className="cy-chat_container">
      {/* Greeting */}
      {showGreeting && (
        <div className="cy-chat-greeting">
          <h2 className="cy-modal-header">How can I assist you today?</h2>
          <p>Ask anything about Odoo and Cybrosys.</p>
        </div>
      )}

      {/* Chat List */}
      <div className="cy-chating--content" style={{ display: showGreeting ? 'none' : 'block' }}
        ref={scrollContainerRef}
        >
        <ul className="cy-chat_list" id="chatList" ref={chatListRef}>
          {renderMessages()}

          {/* Loading/Status Message */}
          {currentStatus && aiTyping && (
            <li className="chat-reply">
              <div className="msg-list msg-loading">
                <div className="oh-chat__typing-animation"></div>
                <span>{currentStatus}</span>
              </div>
            </li>
          )}
        </ul>
      </div>

      {/* Message Input */}
      <div className="cy-ai-message_input">
        <ChatInput
          onSendMessage={handleSendMessage}
          isLoading={isSendingMessage}
        />
      </div>
    </div>
  );
};

export default ChatWindow;
