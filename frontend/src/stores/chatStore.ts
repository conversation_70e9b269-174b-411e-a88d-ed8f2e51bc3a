import { create } from 'zustand';
import type { AppMessage, ChatState } from '../types/appTypes';
import { fetchChatHistory, fetchConversationMessages, postChatMessageSSE, submitFeedback, deleteConversation } from '../services/apiService';
import { getUserId } from '../utils/userIdManager';

export const useChatStore = create<ChatState>((set, get) => ({
  userId: null,
  conversations: [],
  currentConversationId: null,
  messages: [],
  isLoadingHistory: false,
  isLoadingMessages: false,
  isSendingMessage: false,
  currentStatus: null,
  aiTyping: false,
  searchResults: [],

  initializeUser: () => {
    const id = getUserId();
    set({ userId: id });
    if (id) get().loadHistory();
  },

  loadHistory: async () => {
    const { userId } = get();
    if (!userId) return;
    
    set({ isLoadingHistory: true });
    try {
      const history = await fetchChatHistory(userId);
      set({ conversations: history, isLoadingHistory: false });
    } catch (error) {
      console.error("Failed to load history", error);
      set({ isLoadingHistory: false });
    }
  },

  loadConversation: async (conversationId) => {
    const { userId } = get();
    if (!userId) return;
    
    set({ isLoadingMessages: true, currentConversationId: conversationId, messages: [] });
    try {
      const convData = await fetchConversationMessages(userId, conversationId);
      // Transform ApiMessage to AppMessage
      set({ 
        messages: convData.messages.map(m => ({
          ...m, 
          type: m.role as 'user' | 'assistant' | 'system',
          isLoading: false
        })), 
        isLoadingMessages: false 
      });
    } catch (error) {
      console.error("Failed to load conversation", error);
      set({ isLoadingMessages: false });
    }
  },

  startNewChat: () => {
    set({ currentConversationId: null, messages: [], searchResults: [], currentStatus: null, aiTyping: false });
  },

  sendMessage: async (text, model) => {
    const { userId, currentConversationId, messages } = get();
    if (!userId || !text.trim()) return;

    const userMessage: AppMessage = {
      id: `client-${Date.now()}`, // Client-side temporary ID
      role: 'user',
      content: text,
      timestamp: new Date().toISOString(),
      type: 'user',
    };
    
    set({ 
      messages: [...messages, userMessage], 
      isSendingMessage: true, 
      aiTyping: true, 
      currentStatus: "Sending...",
      searchResults: []
    });

    await postChatMessageSSE(
      { message: text, userId, conversationId: currentConversationId, model },
      {
        onOpen: () => set({ currentStatus: "Connecting to AI..." }),
        
        onStatus: (status) => set({ currentStatus: status, aiTyping: true }),
        
        onSearchPreview: (preview) => {
          set(state => ({
            searchResults: [...state.searchResults, { 
              url: preview.url, 
              title: preview.title, 
              snippet: '' 
            }]
          }));
        },
        
        onToken: (token, isFirst) => {
          if (isFirst) { // First token for a new AI message
            const aiMessageShell: AppMessage = {
              id: `server-${Date.now()}`, // Temporary, will be updated by onEnd
              role: 'assistant',
              content: token,
              timestamp: new Date().toISOString(),
              type: 'assistant',
            };
            set(state => ({ messages: [...state.messages, aiMessageShell], aiTyping: true }));
          } else {
            get().addTokenToLastMessage(token);
          }
        },
        
        onSource: (source) => {
          // Update search results with source information
          set(state => {
            const updatedResults = state.searchResults.map(result => 
              result.url === source.url 
                ? { ...result, snippet: source.snippet } 
                : result
            );
            
            // If the URL wasn't in our results yet, add it
            if (!updatedResults.some(r => r.url === source.url)) {
              updatedResults.push({
                url: source.url,
                title: source.title,
                snippet: source.snippet
              });
            }
            
            return { searchResults: updatedResults };
          });
        },
        
        onEnd: (data) => {
          set(state => {
            // Find the last assistant message and update its ID
            const updatedMessages = state.messages.map(m =>
              m.role === 'assistant' && m === state.messages[state.messages.length - 1]
                ? { ...m, id: data.aiMessageId }
                : m
            );
            localStorage.setItem('currentConversationId', data.conversationId);
            return {
              isSendingMessage: false,
              aiTyping: false,
              currentStatus: "Completed",
              messages: updatedMessages,
              currentConversationId: data.conversationId, // Update if it was a new chat
            };
          });

          // Clear the status after a short delay
          setTimeout(() => {
            set({ currentStatus: null });
          }, 2000);

          // Refresh history list
          get().loadHistory();
        },
        
        onError: (error) => {
          set({ isSendingMessage: false, aiTyping: false, currentStatus: `Error: ${error.message}` });

          const errorMessage: AppMessage = {
            id: `error-${Date.now()}`,
            role: 'assistant',
            content: `Sorry, an error occurred: ${error.message}`,
            timestamp: new Date().toISOString(),
            type: 'error',
          };

          set(state => ({ messages: [...state.messages, errorMessage] }));

          // Clear the error status after a longer delay to give user time to read it
          setTimeout(() => {
            set({ currentStatus: null });
          }, 5000);
        }
      }
    );
  },

  addTokenToLastMessage: (token) => {
    set(state => {
      const lastMsgIndex = state.messages.length - 1;
      if (lastMsgIndex >= 0 && state.messages[lastMsgIndex].role === 'assistant') {
        const updatedMessages = [...state.messages];
        updatedMessages[lastMsgIndex] = {
          ...updatedMessages[lastMsgIndex],
          content: updatedMessages[lastMsgIndex].content + token,
        };
        return { messages: updatedMessages };
      }
      return state; // Should not happen if stream logic is correct
    });
  },

  setAiMessageComplete: (aiMessageId) => {
    // Update the message with the given ID to mark it as complete
    set(state => ({
      messages: state.messages.map(m => 
        m.id === aiMessageId 
          ? { ...m, isLoading: false } 
          : m
      )
    }));
  },

  submitFeedback: async (messageId, feedbackType) => {
    const { userId } = get();
    if (!userId) return;

    // Optimistically update the UI
    get().setMessageFeedback(messageId, feedbackType);

    try {
      await submitFeedback(messageId, feedbackType);
    } catch (error) {
      console.error("Failed to submit feedback", error);
      // Revert the change on error
      const originalFeedback = get().messages.find(m => m.id === messageId)?.feedback === feedbackType ? null : get().messages.find(m => m.id === messageId)?.feedback || null;
      get().setMessageFeedback(messageId, originalFeedback);
    }
  },

  setMessageFeedback: (messageId: string, feedbackType: 'like' | 'dislike' | null) => {
    set(state => ({
      messages: state.messages.map(m =>
        m.id === messageId
          ? { ...m, feedback: m.feedback === feedbackType ? null : feedbackType } // Toggle feedback
          : m
      )
    }));
  },

  deleteConversation: async (conversationId: string) => {
    const { userId } = get();
    if (!userId) return;
    
    try {
      await deleteConversation(userId, conversationId);
      
      // Remove the conversation from the list
      set(state => ({
        conversations: state.conversations.filter(c => c.id !== conversationId)
      }));
      
      // If the deleted conversation was the current one, start a new chat
      if (get().currentConversationId === conversationId) {
        get().startNewChat();
      }
    } catch (error) {
      console.error("Failed to delete conversation", error);
    }
  }
}));
