[Unit]
Description=Cybro AI Chat Service
After=network.target

[Service]
User=your_user
Group=your_group
WorkingDirectory=/home/<USER>/AI/cybro-ai-chat-main
ExecStartPre=/bin/bash -c 'cd /home/<USER>/AI/cybro-ai-chat-main/frontend && npm install && npm run build'
ExecStart=/bin/bash -c 'cd /home/<USER>/AI/cybro-ai-chat-main/backend_js && npm install && npm start'
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=cybro-chat

[Install]
WantedBy=multi-user.target